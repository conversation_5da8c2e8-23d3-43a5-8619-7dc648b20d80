import 'package:flutter/material.dart';
import 'package:jyt_components_package/theme/colors.dart';

/// 表单校验时错误信息提示组件
class ValidatorMsg extends StatelessWidget {
  final FormFieldState field;

  const ValidatorMsg(this.field, {super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 18,
      child: Align(
        alignment: Alignment.topLeft,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: field.hasError ? 1.0 : 0.0,
          child: Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              field.hasError ? (field.errorText ?? '') : '',
              style: TextStyle(fontSize: 11, color: AppColors.error, height: 1.2),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}
