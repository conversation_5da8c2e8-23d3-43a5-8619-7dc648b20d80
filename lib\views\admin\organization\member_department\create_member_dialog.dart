import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/components/form/validator_msg/validator_msg.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/components/selector/employee_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';
import 'package:octasync_client/views/admin/organization/member_department/member_department_emun.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

// 人员创建
class CreateMemberDialog extends StatefulWidget {
  final void Function()? onSuccess; // 提交成功回调
  final Widget? child;

  const CreateMemberDialog({super.key, this.onSuccess, this.child});

  @override
  State<CreateMemberDialog> createState() => MemberDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class MemberDialogState extends State<CreateMemberDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  Employee _employeeData = Employee();

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  /// 选中的部门列表
  List<DepartmentModel> checkedDepartments = [];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _employeeNameController = TextEditingController();
  final SelectController<int> _sexSelectController = SelectController<int>();

  /// 重置数据
  void resetFormData() {
    _employeeData = Employee();
    _employeeNameController.text = '';
    checkedDepartments = [];
  }

  @override
  void dispose() {
    _employeeNameController.dispose();
    super.dispose();
  }

  /// 添加
  Future<void> createRequest(BuildContext context, StateSetter setDialogState) async {
    if (!_formKey.currentState!.validate()) return;

    // try {
    //   departmentModel.parentIdList = checkedDepartments.map((t) => t.id!).toList();
    //   final params = departmentModel.toJson();
    //   params.remove('Id');
    //   await DepartmentApi.add(params);
    //   ToastManager.success('提交成功');
    //   widget.onSuccess?.call();
    //   resetFormData();
    //   if (!isAddNext) context.pop();
    // } finally {
    //   setState(() {
    //     btnLoading = false;
    //   });
    // }
  }

  /// 打开添加部门弹窗
  void showDepartmentDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
  }) {
    _dialogType = type;

    // 重置表单数据
    resetFormData();

    double labelWidth = 80;

    /// 间距
    double spacing = 20;

    /// 性别
    Widget buildSexSelect() {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: labelWidth,
            child: Row(
              children: [
                Text('性别'),
                Text(' *', style: TextStyle(fontSize: 20, color: AppColors.error)),
              ],
            ),
          ),
          Expanded(
            child: FormField(
              validator: (value) {
                print(
                  'validator called with value: $value, controller.value: ${_sexSelectController.value}',
                );
                // 使用 controller 的值进行验证
                if (_sexSelectController.value == null) {
                  return '请选择性别';
                }
                return null;
              },
              builder: (FormFieldState field) {
                return Column(
                  children: [
                    AppSelect<int>(
                      placeholder: '性别',
                      options: MemberDepartmentEmun.sexOptions,
                      controller: _sexSelectController,
                      onChanged: (value) {
                        print('value????>>>>????>>>$value');
                        field.didChange(value);
                        _employeeData.sexEnum = value;
                      },
                    ),
                    ValidatorMsg(field),
                  ],
                );
              },
            ),
          ),
        ],
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加成员' : '编辑成员',
      isDrawer: true,
      barrierDismissible: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppInput(
                        label: "姓名",
                        labelWidth: labelWidth,
                        labelPosition: LabelPosition.left,
                        hintText: "姓名",
                        size: InputSize.medium,
                        controller: _employeeNameController,
                        maxLength: 30,
                        validator: (value) {
                          if (_employeeData.name.isEmpty) {
                            return '请输入姓名';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          _employeeData.name = value;
                        },
                      ),
                      buildSexSelect(),
                      Row(
                        children: [
                          SizedBox(width: labelWidth, child: Text('所属部门')),
                          Expanded(
                            child: DepartmentSelector(
                              checkStrictly: true,
                              defaultCheckedDepartmentIds:
                                  checkedDepartments.map((t) => t.id!).toList(),
                              onChange: (selectedDepartments) {
                                setDialogState(() {
                                  checkedDepartments = selectedDepartments;
                                  print('Selected Departments: ${jsonEncode(checkedDepartments)}');
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    if (_dialogType == DialogTypeEmun.create) ...[
                      Checkbox(
                        value: isAddNext,
                        onChanged: (value) {
                          setDialogState(() {
                            isAddNext = !isAddNext;
                          });
                        },
                      ),
                      Text('继续新建下一条'),
                      const SizedBox(width: 10),
                    ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
